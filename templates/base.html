<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Garden Planner{% endblock %}</title>
    <!-- Favicon using plant emoji as SVG -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🌱</text></svg>">
    <!-- Material 3 Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap" rel="stylesheet">
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="stylesheet" href="/static/css/wizard.css">
    <script src="https://unpkg.com/htmx.org@1.9.2"></script>
    <script src="/static/js/scripts.js" defer></script>
    {% block head %}{% endblock %}
</head>
<body class="bg-surface-50 dark:bg-surface-950 text-surface-900 dark:text-surface-100 transition-colors duration-200 font-sans">
    <!-- Navigation -->
    <nav class="bg-primary-700 dark:bg-primary-800 text-white shadow-elevation-2">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="/" class="text-title-large font-medium text-white hover:text-primary-200 transition-colors flex items-center gap-2">
                        <span class="material-icons text-2xl">eco</span>
                        Garden Planner
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-6">
                    {% if user_context.is_authenticated %}
                        <!-- Main Navigation Links -->
                        <div class="flex items-center space-x-6">
                            <a href="/" class="text-body-medium text-white hover:text-primary-200 transition-colors px-3 py-2 rounded-md">Dashboard</a>
                            <a href="/plants/list" class="text-body-medium text-white hover:text-primary-200 transition-colors px-3 py-2 rounded-md">Plants</a>
                            <a href="/seeds/list" class="text-body-medium text-white hover:text-primary-200 transition-colors px-3 py-2 rounded-md">Seeds</a>
                            <a href="/property" class="text-body-medium text-white hover:text-primary-200 transition-colors px-3 py-2 rounded-md">Properties</a>
                            <a href="/seasons/list" class="text-body-medium text-white hover:text-primary-200 transition-colors px-3 py-2 rounded-md">Seasons</a>
                            <a href="/season_plans" class="text-body-medium text-white hover:text-primary-200 transition-colors px-3 py-2 rounded-md">Season Plans</a>
                            <a href="/wishlist/plants" class="text-body-medium text-white hover:text-primary-200 transition-colors px-3 py-2 rounded-md">Wishlist</a>
                            {% if user_context.is_admin %}
                                <a href="/admin" class="text-body-medium text-white hover:text-primary-200 transition-colors px-3 py-2 rounded-md">Admin</a>
                            {% endif %}
                        </div>

                        <!-- Notifications -->
                        <div class="relative" id="notifications-container">
                            <button id="notifications-button" class="text-white hover:text-primary-200 p-3 rounded-lg transition-colors relative">
                                <span class="material-icons text-xl">notifications</span>
                                <!-- Notification badge -->
                                <span id="notification-badge" class="absolute -top-1 -right-1 bg-error-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center hidden shadow-elevation-2">0</span>
                            </button>
                            <!-- Notifications Dropdown -->
                            <div id="notifications-menu" class="absolute right-0 top-full mt-2 w-80 bg-surface-50 dark:bg-surface-800 rounded-lg shadow-elevation-3 py-1 z-50 hidden border border-surface-200 dark:border-surface-700 max-h-96 overflow-y-auto">
                                <div class="px-4 py-3 border-b border-surface-200 dark:border-surface-700">
                                    <h3 class="text-title-small font-medium text-surface-900 dark:text-surface-100">Notifications</h3>
                                </div>
                                <div id="notifications-list" class="py-1">
                                    <!-- Notifications will be loaded here -->
                                    <div class="px-4 py-3 text-body-medium text-surface-500 dark:text-surface-400 text-center">
                                        No new notifications
                                    </div>
                                </div>
                                <div class="border-t border-surface-200 dark:border-surface-700 px-4 py-2">
                                    <a href="/notifications" class="text-body-small text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300">View all notifications</a>
                                </div>
                            </div>
                        </div>

                        <!-- User Menu -->
                        <div class="relative" id="user-menu-container">
                            <button id="user-menu-button" class="flex items-center space-x-3 text-white hover:text-primary-200 transition-colors px-3 py-2 rounded-lg">
                                <div class="w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center text-label-large font-medium relative shadow-elevation-1">
                                    <span class="material-icons text-lg text-white">eco</span>
                                </div>
                                <span class="hidden lg:block text-body-medium">{{ user_context.username }}</span>
                                <span class="material-icons text-lg">expand_more</span>
                            </button>
                            <!-- User Dropdown Menu -->
                            <div id="user-menu" class="absolute right-0 top-full mt-2 w-64 bg-surface-50 dark:bg-surface-800 rounded-lg shadow-elevation-3 py-1 z-50 hidden border border-surface-200 dark:border-surface-700">
                                <!-- User Info Section -->
                                <div class="px-4 py-3 border-b border-surface-200 dark:border-surface-700">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-12 h-12 bg-primary-600 rounded-full flex items-center justify-center text-white font-semibold shadow-elevation-1">
                                            <span class="material-icons text-xl">eco</span>
                                        </div>
                                        <div>
                                            <p class="text-body-medium font-medium text-surface-900 dark:text-surface-100">{{ user_context.username }}</p>
                                            <p class="text-body-small text-surface-500 dark:text-surface-400">{{ user_context.role|title }}</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Household Switcher -->
                                <div class="px-4 py-3 border-b border-surface-200 dark:border-surface-700">
                                    <p class="text-label-small font-medium text-surface-500 dark:text-surface-400 uppercase tracking-wide mb-2">Current Household</p>
                                    {% if user_context.current_household_name %}
                                        <div class="flex items-center space-x-2 text-body-medium text-surface-700 dark:text-surface-200 mb-2">
                                            <span class="material-icons text-lg text-primary-500">home</span>
                                            <span class="font-medium">{{ user_context.current_household_name }}</span>
                                        </div>
                                    {% endif %}
                                    <a href="/households" class="flex items-center space-x-2 text-body-small text-surface-600 dark:text-surface-300 hover:bg-surface-100 dark:hover:bg-surface-700 rounded-md px-3 py-2 transition-colors">
                                        <span class="material-icons text-base">swap_horiz</span>
                                        <span>Switch Household</span>
                                    </a>
                                </div>

                                <!-- Menu Items -->
                                <div class="py-1">
                                    <a href="/profile" class="flex items-center px-4 py-3 text-body-medium text-surface-700 dark:text-surface-200 hover:bg-surface-100 dark:hover:bg-surface-700 transition-colors">
                                        <span class="material-icons text-lg mr-3">person</span>
                                        Profile Settings
                                    </a>
                                    <a href="/settings" class="flex items-center px-4 py-3 text-body-medium text-surface-700 dark:text-surface-200 hover:bg-surface-100 dark:hover:bg-surface-700 transition-colors">
                                        <span class="material-icons text-lg mr-3">settings</span>
                                        Account Settings
                                    </a>
                                    <a href="/households" class="flex items-center px-4 py-3 text-body-medium text-surface-700 dark:text-surface-200 hover:bg-surface-100 dark:hover:bg-surface-700 transition-colors">
                                        <span class="material-icons text-lg mr-3">groups</span>
                                        Manage Households
                                    </a>
                                    <button id="theme-toggle-menu" class="w-full flex items-center px-4 py-3 text-body-medium text-surface-700 dark:text-surface-200 hover:bg-surface-100 dark:hover:bg-surface-700 transition-colors">
                                        <span id="theme-toggle-dark-icon-menu" class="material-icons text-lg mr-3">dark_mode</span>
                                        <span id="theme-toggle-light-icon-menu" class="material-icons text-lg mr-3 hidden">light_mode</span>
                                        <span id="theme-toggle-text">Dark Mode</span>
                                    </button>
                                </div>

                                <hr class="my-1 border-surface-200 dark:border-surface-700">

                                <div class="py-1">
                                    <a href="/auth/logout" class="flex items-center px-4 py-3 text-body-medium text-surface-700 dark:text-surface-200 hover:bg-surface-100 dark:hover:bg-surface-700 transition-colors">
                                        <span class="material-icons text-lg mr-3">logout</span>
                                        Sign Out
                                    </a>
                                </div>
                            </div>
                        </div>
                    {% else %}
                        <!-- Guest Navigation -->
                        <div class="flex items-center space-x-4">
                            <!-- Theme Toggle for guests -->
                            <button id="theme-toggle" type="button" class="text-white hover:text-primary-200 p-3 rounded-lg transition-colors">
                                <span id="theme-toggle-dark-icon" class="material-icons text-lg">dark_mode</span>
                                <span id="theme-toggle-light-icon" class="material-icons text-lg hidden">light_mode</span>
                            </button>
                            <a href="/auth/register" class="text-body-medium text-white hover:text-primary-200 transition-colors px-3 py-2 rounded-md">Register</a>
                            <a href="/auth/login" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md transition-colors shadow-elevation-2 text-body-medium font-medium">Login</a>
                        </div>
                    {% endif %}
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobile-menu-button" type="button" class="text-white hover:text-primary-200 p-3 rounded-lg transition-colors">
                        <span class="material-icons text-xl">menu</span>
                    </button>
                </div>
            </div>

            <!-- Mobile Navigation Menu -->
            <div id="mobile-menu" class="md:hidden hidden">
                <div class="px-2 pt-2 pb-3 space-y-1 border-t border-primary-600">
                    {% if user_context.is_authenticated %}
                        <a href="/" class="block px-3 py-2 text-body-medium text-white hover:text-primary-200 transition-colors rounded-md">Dashboard</a>
                        <a href="/plants/list" class="block px-3 py-2 text-body-medium text-white hover:text-primary-200 transition-colors rounded-md">Plants</a>
                        <a href="/seeds/list" class="block px-3 py-2 text-body-medium text-white hover:text-primary-200 transition-colors rounded-md">Seeds</a>
                        <a href="/property" class="block px-3 py-2 text-body-medium text-white hover:text-primary-200 transition-colors rounded-md">Properties</a>
                        <a href="/seasons/list" class="block px-3 py-2 text-body-medium text-white hover:text-primary-200 transition-colors rounded-md">Seasons</a>
                        <a href="/season_plans" class="block px-3 py-2 text-body-medium text-white hover:text-primary-200 transition-colors rounded-md">Season Plans</a>
                        <a href="/wishlist/plants" class="block px-3 py-2 text-body-medium text-white hover:text-primary-200 transition-colors rounded-md">Wishlist</a>
                        {% if user_context.is_admin %}
                            <a href="/admin" class="block px-3 py-2 text-body-medium text-white hover:text-primary-200 transition-colors rounded-md">Admin</a>
                        {% endif %}
                        <hr class="my-2 border-primary-600">
                        <a href="/profile" class="block px-3 py-2 text-body-medium text-white hover:text-primary-200 transition-colors rounded-md">Profile</a>
                        <a href="/settings" class="block px-3 py-2 text-body-medium text-white hover:text-primary-200 transition-colors rounded-md">Settings</a>
                        <a href="/households" class="block px-3 py-2 text-body-medium text-white hover:text-primary-200 transition-colors rounded-md">Households</a>
                        <a href="/auth/logout" class="block px-3 py-2 text-body-medium text-white hover:text-primary-200 transition-colors rounded-md">Logout</a>
                    {% else %}
                        <a href="/auth/register" class="block px-3 py-2 text-body-medium text-white hover:text-primary-200 transition-colors rounded-md">Register</a>
                        <a href="/auth/login" class="block px-3 py-2 text-body-medium text-white hover:text-primary-200 transition-colors rounded-md">Login</a>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>
<main class="container mx-auto px-6 py-8">
    {% block content %}{% endblock %}
</main>

<script>
// Enhanced navigation functionality
document.addEventListener('DOMContentLoaded', function() {
    // User menu dropdown functionality with hover support
    const userMenuButton = document.getElementById('user-menu-button');
    const userMenu = document.getElementById('user-menu');
    const userMenuContainer = document.getElementById('user-menu-container');
    let hoverTimeout;

    if (userMenuButton && userMenu && userMenuContainer) {
        // Click functionality (primary)
        userMenuButton.addEventListener('click', function(e) {
            e.stopPropagation();
            userMenu.classList.toggle('hidden');
        });

        // Hover functionality (secondary)
        userMenuContainer.addEventListener('mouseenter', function() {
            clearTimeout(hoverTimeout);
            userMenu.classList.remove('hidden');
        });

        userMenuContainer.addEventListener('mouseleave', function() {
            hoverTimeout = setTimeout(function() {
                userMenu.classList.add('hidden');
            }, 300); // 300ms delay before hiding
        });

        // Close menu when clicking outside
        document.addEventListener('click', function() {
            userMenu.classList.add('hidden');
        });

        // Prevent menu from closing when clicking inside it
        userMenu.addEventListener('click', function(e) {
            e.stopPropagation();
        });

        // Prevent dropdown from closing when hovering over it
        userMenu.addEventListener('mouseenter', function() {
            clearTimeout(hoverTimeout);
        });

        userMenu.addEventListener('mouseleave', function() {
            hoverTimeout = setTimeout(function() {
                userMenu.classList.add('hidden');
            }, 300);
        });
    }

    // Mobile menu functionality
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');

    if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener('click', function(e) {
            e.stopPropagation();
            mobileMenu.classList.toggle('hidden');
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', function() {
            mobileMenu.classList.add('hidden');
        });

        // Prevent mobile menu from closing when clicking inside it
        mobileMenu.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    }

    // Close menus when window is resized to desktop size
    window.addEventListener('resize', function() {
        if (window.innerWidth >= 768) { // md breakpoint
            if (mobileMenu) {
                mobileMenu.classList.add('hidden');
            }
        }
    });

    // Notifications functionality
    const notificationsButton = document.getElementById('notifications-button');
    const notificationsMenu = document.getElementById('notifications-menu');
    const notificationsContainer = document.getElementById('notifications-container');
    let notificationTimeout;

    if (notificationsButton && notificationsMenu && notificationsContainer) {
        // Click functionality
        notificationsButton.addEventListener('click', function(e) {
            e.stopPropagation();
            notificationsMenu.classList.toggle('hidden');
            if (!notificationsMenu.classList.contains('hidden')) {
                loadNotifications();
            }
        });

        // Close menu when clicking outside
        document.addEventListener('click', function() {
            notificationsMenu.classList.add('hidden');
        });

        // Prevent menu from closing when clicking inside it
        notificationsMenu.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    }

    // Theme toggle in user menu
    const themeToggleMenu = document.getElementById('theme-toggle-menu');
    if (themeToggleMenu) {
        themeToggleMenu.addEventListener('click', function() {
            // Trigger the main theme toggle functionality
            const themeToggle = document.getElementById('theme-toggle');
            if (themeToggle) {
                themeToggle.click();
            }
        });
    }

    // Load notifications function
    function loadNotifications() {
        fetch('/api/notifications/recent')
            .then(response => response.json())
            .then(data => {
                const notificationsList = document.getElementById('notifications-list');
                const notificationBadge = document.getElementById('notification-badge');

                if (data.notifications && data.notifications.length > 0) {
                    notificationsList.innerHTML = data.notifications.map(notification => `
                        <div class="px-4 py-3 hover:bg-surface-100 dark:hover:bg-surface-700 border-b border-surface-200 dark:border-surface-600 cursor-pointer" onclick="markAsRead(${notification.id})">
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    ${getNotificationIcon(notification.type)}
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-body-medium font-medium text-surface-900 dark:text-surface-100">${notification.title}</p>
                                    <p class="text-body-medium text-surface-500 dark:text-surface-400">${notification.message}</p>
                                    <p class="text-body-small text-surface-400 dark:text-surface-500 mt-1">${formatTime(notification.created_at)}</p>
                                </div>
                            </div>
                        </div>
                    `).join('');

                    // Update badge
                    const unreadCount = data.notifications.filter(n => !n.read).length;
                    if (unreadCount > 0) {
                        notificationBadge.textContent = unreadCount;
                        notificationBadge.classList.remove('hidden');
                    } else {
                        notificationBadge.classList.add('hidden');
                    }
                } else {
                    notificationsList.innerHTML = `
                        <div class="px-4 py-3 text-body-medium text-surface-500 dark:text-surface-400 text-center">
                            No new notifications
                        </div>
                    `;
                    notificationBadge.classList.add('hidden');
                }
            })
            .catch(error => {
                console.error('Error loading notifications:', error);
            });
    }

    function getNotificationIcon(type) {
        switch(type) {
            case 'watering':
                return '<span class="material-icons text-lg text-primary-500">water_drop</span>';
            case 'fertilizing':
                return '<span class="material-icons text-lg text-secondary-500">eco</span>';
            case 'harvest':
                return '<span class="material-icons text-lg text-tertiary-500">agriculture</span>';
            default:
                return '<span class="material-icons text-lg text-surface-500">notifications</span>';
        }
    }

    function formatTime(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;

        if (diff < 60000) return 'Just now';
        if (diff < 3600000) return Math.floor(diff / 60000) + 'm ago';
        if (diff < 86400000) return Math.floor(diff / 3600000) + 'h ago';
        return Math.floor(diff / 86400000) + 'd ago';
    }

    function markAsRead(notificationId) {
        fetch(`/api/notifications/${notificationId}/read`, { method: 'POST' })
            .then(() => loadNotifications())
            .catch(error => console.error('Error marking notification as read:', error));
    }

    // Load notifications on page load
    if (document.getElementById('notifications-button')) {
        loadNotifications();
        // Refresh notifications every 30 seconds
        setInterval(loadNotifications, 30000);
    }
});
</script>
</body>
</html>
