{% extends "base.html" %}
{% block title %}Plants{% endblock %}
{% block content %}
<div class="max-w-6xl mx-auto">
    <div class="flex justify-between items-center mb-8">
        <div>
            <h1 class="text-display-small font-normal text-surface-900 dark:text-surface-100">Plant Database</h1>
            <p class="text-body-large text-surface-600 dark:text-surface-400 mt-2">Manage your personal plant collection</p>
        </div>
        <a href="/plants/new" class="material-button-filled flex items-center gap-2">
            <span class="material-icons">add</span>
            Add New Plant
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
        <div class="material-card p-6">
            <div class="flex items-center">
                <div class="p-4 rounded-full bg-primary-100 dark:bg-primary-900">
                    <span class="material-icons text-2xl text-primary-600 dark:text-primary-300">local_florist</span>
                </div>
                <div class="ml-6">
                    <p class="text-label-large font-medium text-surface-600 dark:text-surface-400">Total Plants</p>
                    <p class="text-headline-small font-normal text-surface-900 dark:text-surface-100">{{ plants|length }}</p>
                </div>
            </div>
        </div>

        <div class="material-card p-6">
            <div class="flex items-center">
                <div class="p-4 rounded-full bg-secondary-100 dark:bg-secondary-900">
                    <span class="material-icons text-2xl text-secondary-600 dark:text-secondary-300">science</span>
                </div>
                <div class="ml-6">
                    <p class="text-label-large font-medium text-surface-600 dark:text-surface-400">With Latin Names</p>
                    <p class="text-headline-small font-normal text-surface-900 dark:text-surface-100">{{ plants_with_latin_names }}</p>
                </div>
            </div>
        </div>

        <div class="material-card p-6">
            <div class="flex items-center">
                <div class="p-4 rounded-full bg-tertiary-100 dark:bg-tertiary-900">
                    <span class="material-icons text-2xl text-tertiary-600 dark:text-tertiary-300">category</span>
                </div>
                <div class="ml-6">
                    <p class="text-label-large font-medium text-surface-600 dark:text-surface-400">Varieties</p>
                    <p class="text-headline-small font-normal text-surface-900 dark:text-surface-100">{{ plants_with_varieties }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Plants Table -->
    <div class="material-card-elevated overflow-hidden">
        <div class="px-6 py-4 border-b border-surface-200 dark:border-surface-700">
            <h2 class="text-title-large font-medium text-surface-900 dark:text-surface-100">Plant Collection</h2>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-surface-200 dark:divide-surface-700">
                <thead class="bg-surface-100 dark:bg-surface-700">
                    <tr>
                        <th class="px-6 py-4 text-left text-label-small font-medium text-surface-500 dark:text-surface-300 uppercase tracking-wider">
                            Plant
                        </th>
                        <th class="px-6 py-4 text-left text-label-small font-medium text-surface-500 dark:text-surface-300 uppercase tracking-wider">
                            Latin Name
                        </th>
                        <th class="px-6 py-4 text-left text-label-small font-medium text-surface-500 dark:text-surface-300 uppercase tracking-wider">
                            Variety
                        </th>
                        <th class="px-6 py-4 text-left text-label-small font-medium text-surface-500 dark:text-surface-300 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-surface-50 dark:bg-surface-800 divide-y divide-surface-200 dark:divide-surface-700">
                    {% for plant in plants %}
                    <tr class="hover:bg-surface-100 dark:hover:bg-surface-700 transition-colors">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-12 w-12">
                                    <div class="h-12 w-12 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
                                        <span class="material-icons text-xl text-primary-600 dark:text-primary-300">local_florist</span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-body-medium font-medium text-surface-900 dark:text-surface-100">
                                        {{ plant.name }}
                                    </div>
                                    {% if plant.note %}
                                    <div class="text-body-small text-surface-500 dark:text-surface-400">
                                        {% if plant.note|length > 50 %}{{ plant.note|slice(end=50) }}...{% else %}{{ plant.note }}{% endif %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-body-medium text-surface-900 dark:text-surface-100">
                            {{ plant.latin_name | default(value="Not specified") }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-body-medium text-surface-900 dark:text-surface-100">
                            {{ plant.variety | default(value="Standard") }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-body-medium font-medium">
                            <a href="/plants/{{ plant.id }}/edit" class="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 mr-4">
                                Edit
                            </a>
                            <form method="post" action="/plants/{{ plant.id }}/delete" class="inline" onsubmit="return confirm('Are you sure you want to delete this plant?')">
                                <button type="submit" class="text-error-600 hover:text-error-900 dark:text-error-400 dark:hover:text-error-300">
                                    Delete
                                </button>
                            </form>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        {% if plants|length == 0 %}
        <div class="px-6 py-12 text-center">
            <span class="material-icons text-6xl text-surface-400 dark:text-surface-600 mb-4 block">local_florist</span>
            <p class="text-body-large text-surface-500 dark:text-surface-400 mb-4">No plants found in your collection.</p>
            <a href="/plants/new" class="material-button-text">
                Add your first plant
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
