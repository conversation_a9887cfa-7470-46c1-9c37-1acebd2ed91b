/* Garden Planner Web Application - Material 3 Expressive Design */

/* Material 3 Component Extensions */
.material-card {
    @apply bg-surface-50 dark:bg-surface-800 rounded-lg shadow-elevation-2 border border-surface-200 dark:border-surface-700;
}

.material-card-elevated {
    @apply bg-surface-50 dark:bg-surface-800 rounded-lg shadow-elevation-3 border border-surface-200 dark:border-surface-700;
}

.material-button-filled {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-full shadow-elevation-2 hover:shadow-elevation-3 transition-all duration-200 text-label-large;
}

.material-button-outlined {
    @apply border-2 border-primary-600 text-primary-600 hover:bg-primary-50 dark:hover:bg-primary-900 font-medium py-3 px-6 rounded-full transition-all duration-200 text-label-large;
}

.material-button-text {
    @apply text-primary-600 hover:bg-primary-50 dark:hover:bg-primary-900 font-medium py-3 px-6 rounded-full transition-all duration-200 text-label-large;
}

.material-fab {
    @apply bg-primary-600 hover:bg-primary-700 text-white rounded-full shadow-elevation-3 hover:shadow-elevation-4 transition-all duration-200 flex items-center justify-center;
}

/* Material 3 Form Components */
.material-input {
    @apply w-full px-4 py-3 border border-surface-300 dark:border-surface-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-surface-800 dark:text-surface-100 text-body-large transition-all duration-200;
}

.material-label {
    @apply block text-label-large font-medium text-surface-700 dark:text-surface-300 mb-2;
}

.material-select {
    @apply w-full px-4 py-3 border border-surface-300 dark:border-surface-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-surface-800 dark:text-surface-100 text-body-large transition-all duration-200;
}

/* Drawing tools specific styles */
.tool-button {
    @apply px-4 py-3 border border-surface-300 dark:border-surface-600 rounded-lg text-label-large font-medium text-surface-700 dark:text-surface-200 bg-surface-50 dark:bg-surface-800 hover:bg-surface-100 dark:hover:bg-surface-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200;
}

.tool-button.active {
    @apply bg-primary-600 text-white border-primary-600 shadow-elevation-2;
}

.canvas-container {
    @apply relative border-2 border-surface-300 dark:border-surface-600 rounded-lg overflow-hidden shadow-elevation-1;
    min-height: 400px;
}

/* Button styles */
.btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg shadow-elevation-2 hover:shadow-elevation-3 transition-all duration-200 text-label-large;
}

.btn-secondary {
    @apply bg-secondary-600 hover:bg-secondary-700 text-white font-medium py-3 px-6 rounded-lg shadow-elevation-2 hover:shadow-elevation-3 transition-all duration-200 text-label-large;
}

.btn-success {
    @apply bg-tertiary-600 hover:bg-tertiary-700 text-white font-medium py-3 px-6 rounded-lg shadow-elevation-2 hover:shadow-elevation-3 transition-all duration-200 text-label-large;
}

.btn-danger {
    @apply bg-error-600 hover:bg-error-700 text-white font-medium py-3 px-6 rounded-lg shadow-elevation-2 hover:shadow-elevation-3 transition-all duration-200 text-label-large;
}

/* Form styles (legacy compatibility) */
.form-input {
    @apply w-full px-4 py-3 border border-surface-300 dark:border-surface-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-surface-800 dark:text-surface-100 text-body-large transition-all duration-200;
}

.form-label {
    @apply block text-label-large font-medium text-surface-700 dark:text-surface-300 mb-2;
}

/* Navigation styles */
.nav-link {
    @apply text-white hover:text-primary-200 px-3 py-2 rounded-md text-body-medium font-medium transition-colors;
}

.nav-link.active {
    @apply bg-primary-700 text-white shadow-elevation-1;
}

/* Card styles */
.card {
    @apply bg-surface-50 dark:bg-surface-800 rounded-lg shadow-elevation-2 border border-surface-200 dark:border-surface-700;
}

.card-header {
    @apply px-6 py-4 border-b border-surface-200 dark:border-surface-600;
}

.card-body {
    @apply px-6 py-4;
}

/* Notification styles */
.notification-item {
    @apply px-4 py-3 hover:bg-surface-100 dark:hover:bg-surface-700 border-b border-surface-200 dark:border-surface-600 cursor-pointer transition-colors;
}

/* Statistics dashboard */
.stat-card {
    @apply bg-surface-50 dark:bg-surface-800 rounded-lg shadow-elevation-2 p-6 text-center;
}

.stat-value {
    @apply text-display-small font-normal text-surface-900 dark:text-surface-100;
}

.stat-label {
    @apply text-body-medium text-surface-600 dark:text-surface-400;
}

/* Responsive design */
@media (max-width: 768px) {
    .canvas-container {
        min-height: 300px;
    }
    
    .tool-button {
        @apply px-2 py-1 text-xs;
    }
}

/* Dark mode specific adjustments */
@media (prefers-color-scheme: dark) {
    .canvas-container {
        @apply border-sage-600;
    }
}

/* Animation utilities */
.transition-all {
    transition: all 0.2s ease-in-out;
}

.transition-colors {
    transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
}

/* Grid and drawing specific styles */
.grid-toggle-btn.active {
    background-color: #3b82f6 !important;
    color: white !important;
    border: 2px solid #3b82f6 !important;
}

/* Wishlist styles */
.wishlist-item {
    @apply flex items-center justify-between p-4 bg-sage-50 dark:bg-sage-700 rounded-material border border-sage-200 dark:border-sage-600;
}

/* Property visualization */
.property-shape {
    fill: rgba(59, 130, 246, 0.2);
    stroke: rgba(59, 130, 246, 0.8);
    stroke-width: 2;
}

.growing-area-shape {
    fill: rgba(34, 197, 94, 0.3);
    stroke: rgba(34, 197, 94, 0.8);
    stroke-width: 2;
}
